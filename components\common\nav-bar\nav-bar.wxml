<!-- 自定义导航栏组件 -->
<view class="nav-bar" style="height: {{navBarHeight + 'px'}}; background-color: {{backgroundColor}}; color: {{textColor}};">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight + 'px'}};"></view>

  <!-- 导航栏内容 -->
  <view class="nav-bar-content" style="height: {{titleBarHeight + 'px'}};">
    <!-- 左侧内容 -->
    <view class="nav-bar-left">
      <view wx:if="{{showBack}}" class="nav-back-btn" bindtap="onBackTap">
        <van-icon name="arrow-left" size="18px" color="{{textColor}}" custom-class="nav-back-icon" />
      </view>
      <view wx:if="{{showHome}}" class="nav-home-btn" bindtap="onHomeTap">
        <van-icon name="wap-home-o" size="18px" color="{{textColor}}" custom-class="nav-home-icon" />
      </view>
      <slot name="left"></slot>
    </view>

    <!-- 中间标题 -->
    <view class="nav-bar-center">
      <text class="nav-bar-title" style="color: {{textColor}};">{{title}}</text>
      <slot name="center"></slot>
    </view>

    <!-- 右侧内容 -->
    <view class="nav-bar-right">
      <slot name="right"></slot>
    </view>
  </view>
</view>
