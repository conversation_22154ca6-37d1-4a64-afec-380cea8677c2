// 组件按需引入配置
// 管理项目中使用的所有组件，实现按需加载

// Vant UI 组件映射
const vantComponentMap = {
  // 基础组件
  'van-button': '@vant/weapp/button/index',
  'van-cell': '@vant/weapp/cell/index',
  'van-cell-group': '@vant/weapp/cell-group/index',
  'van-icon': '@vant/weapp/icon/index',
  'van-image': '@vant/weapp/image/index',
  'van-loading': '@vant/weapp/loading/index',
  'van-toast': '@vant/weapp/toast/index',
  
  // 表单组件
  'van-field': '@vant/weapp/field/index',
  'van-search': '@vant/weapp/search/index',
  'van-switch': '@vant/weapp/switch/index',
  'van-checkbox': '@vant/weapp/checkbox/index',
  'van-checkbox-group': '@vant/weapp/checkbox-group/index',
  'van-radio': '@vant/weapp/radio/index',
  'van-radio-group': '@vant/weapp/radio-group/index',
  'van-picker': '@vant/weapp/picker/index',
  'van-datetime-picker': '@vant/weapp/datetime-picker/index',
  'van-rate': '@vant/weapp/rate/index',
  'van-slider': '@vant/weapp/slider/index',
  'van-stepper': '@vant/weapp/stepper/index',
  'van-uploader': '@vant/weapp/uploader/index',
  
  // 反馈组件
  'van-dialog': '@vant/weapp/dialog/index',
  'van-notify': '@vant/weapp/notify/index',
  'van-popup': '@vant/weapp/popup/index',
  'van-action-sheet': '@vant/weapp/action-sheet/index',
  'van-dropdown-menu': '@vant/weapp/dropdown-menu/index',
  'van-dropdown-item': '@vant/weapp/dropdown-item/index',
  'van-overlay': '@vant/weapp/overlay/index',
  'van-share-sheet': '@vant/weapp/share-sheet/index',
  'van-swipe-cell': '@vant/weapp/swipe-cell/index',
  
  // 展示组件
  'van-progress': '@vant/weapp/progress/index',
  'van-tag': '@vant/weapp/tag/index',
  'van-info': '@vant/weapp/info/index',
  'van-empty': '@vant/weapp/empty/index',
  'van-skeleton': '@vant/weapp/skeleton/index',
  'van-divider': '@vant/weapp/divider/index',
  'van-notice-bar': '@vant/weapp/notice-bar/index',
  'van-panel': '@vant/weapp/panel/index',
  'van-collapse': '@vant/weapp/collapse/index',
  'van-collapse-item': '@vant/weapp/collapse-item/index',
  
  // 导航组件
  'van-tab': '@vant/weapp/tab/index',
  'van-tabs': '@vant/weapp/tabs/index',
  'van-tabbar': '@vant/weapp/tabbar/index',
  'van-tabbar-item': '@vant/weapp/tabbar-item/index',
  'van-nav-bar': '@vant/weapp/nav-bar/index',
  'van-pagination': '@vant/weapp/pagination/index',
  'van-steps': '@vant/weapp/steps/index',
  'van-step': '@vant/weapp/step/index',
  'van-sidebar': '@vant/weapp/sidebar/index',
  'van-sidebar-item': '@vant/weapp/sidebar-item/index',
  'van-tree-select': '@vant/weapp/tree-select/index',
  
  // 业务组件
  'van-card': '@vant/weapp/card/index',
  'van-goods-action': '@vant/weapp/goods-action/index',
  'van-goods-action-button': '@vant/weapp/goods-action-button/index',
  'van-goods-action-icon': '@vant/weapp/goods-action-icon/index',
  'van-submit-bar': '@vant/weapp/submit-bar/index',
  
  // 布局组件
  'van-grid': '@vant/weapp/grid/index',
  'van-grid-item': '@vant/weapp/grid-item/index'
}

// 自定义组件映射
const customComponentMap = {
  'nav-bar': '/components/common/nav-bar/nav-bar',
  'loading': '/components/common/loading/loading',
  'empty': '/components/common/empty/empty'
}

// 页面组件需求配置
const pageComponentConfig = {
  // 主包页面
  'pages/index/index': [
    'van-grid', 'van-grid-item', 'van-card', 'van-tag', 
    'van-progress', 'van-notice-bar', 'nav-bar'
  ],
  'pages/tasks/tasks': [
    'van-search', 'van-empty', 'van-loading', 'van-tag', 
    'van-progress', 'van-button', 'nav-bar'
  ],
  'pages/workspace/workspace': [
    'van-grid', 'van-grid-item', 'van-cell', 'van-cell-group',
    'van-icon', 'van-info', 'nav-bar'
  ],
  
  // 用户页面（已移至主包）
  'pages/messages/messages': [
    'van-cell', 'van-cell-group', 'van-info', 'van-tag',
    'van-empty', 'van-loading', 'nav-bar'
  ],
  'pages/profile/profile': [
    'van-cell', 'van-cell-group', 'van-button', 'van-icon',
    'van-image', 'van-switch', 'van-dialog', 'van-info', 'nav-bar'
  ]
}

// 获取页面组件配置
function getPageComponents(pagePath) {
  const components = {}
  const requiredComponents = pageComponentConfig[pagePath] || []
  
  requiredComponents.forEach(componentName => {
    if (vantComponentMap[componentName]) {
      components[componentName] = vantComponentMap[componentName]
    } else if (customComponentMap[componentName]) {
      components[componentName] = customComponentMap[componentName]
    }
  })
  
  return components
}

// 获取组件使用统计
function getComponentUsageStats() {
  const stats = {}
  
  Object.values(pageComponentConfig).forEach(components => {
    components.forEach(component => {
      stats[component] = (stats[component] || 0) + 1
    })
  })
  
  return stats
}

// 检查未使用的组件
function getUnusedComponents() {
  const usedComponents = new Set()
  Object.values(pageComponentConfig).forEach(components => {
    components.forEach(component => usedComponents.add(component))
  })
  
  const allComponents = [...Object.keys(vantComponentMap), ...Object.keys(customComponentMap)]
  return allComponents.filter(component => !usedComponents.has(component))
}

module.exports = {
  vantComponentMap,
  customComponentMap,
  pageComponentConfig,
  getPageComponents,
  getComponentUsageStats,
  getUnusedComponents
}
