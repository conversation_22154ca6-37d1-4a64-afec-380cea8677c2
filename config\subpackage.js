// 页面配置管理
// 当前项目采用单包架构，所有页面都在主包中

const pageConfig = {
  // 所有页面都在主包中
  pages: [
    'pages/index/index',        // 首页总览
    'pages/tasks/tasks',        // 任务管理
    'pages/workspace/workspace', // 工作台
    'pages/messages/messages',   // 消息中心
    'pages/profile/profile'      // 个人中心
  ],
  description: '单包架构，所有页面都在主包中，确保快速启动和简化维护'
}

// 性能配置
const performanceConfig = {
  maxSize: '2MB',
  description: '主包建议不超过2MB，确保快速启动'
}

// 获取页面配置
function getPageConfig() {
  return pageConfig.pages
}

// 检查页面是否有效
function isValidPage(pagePath) {
  return pageConfig.pages.includes(pagePath)
}

module.exports = {
  pageConfig,
  performanceConfig,
  getPageConfig,
  isValidPage
}
