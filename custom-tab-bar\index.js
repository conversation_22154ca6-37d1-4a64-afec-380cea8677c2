// 自定义 tabBar
Component({
  data: {
    selected: 2, // 默认选中总览页面
    list: [
      {
        pagePath: "/pages/tasks/tasks",
        text: "任务",
        icon: "orders-o",
        selectedIcon: "orders-o"
      },
      {
        pagePath: "/pages/workspace/workspace",
        text: "工作台",
        icon: "apps-o",
        selectedIcon: "apps-o"
      },
      {
        pagePath: "/pages/index/index",
        text: "总览",
        icon: "wap-home-o",
        selectedIcon: "wap-home-o"
      },
      {
        pagePath: "/pages/messages/messages",
        text: "消息",
        icon: "chat-o",
        selectedIcon: "chat-o",
        badge: "" // 可以显示未读消息数
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        icon: "user-o",
        selectedIcon: "user-o"
      }
    ]
  },

  methods: {
    switchTab(e) {
      const index = e.currentTarget.dataset.index
      const pagePath = this.data.list[index].pagePath
      
      wx.switchTab({
        url: pagePath,
        success: () => {
          this.setData({
            selected: index
          })
        }
      })
    },

    // 更新选中状态
    updateSelected(index) {
      this.setData({
        selected: index
      })
    },

    // 更新消息徽章
    updateBadge(index, badge) {
      const list = this.data.list
      list[index].badge = badge
      this.setData({
        list
      })
    }
  }
})
