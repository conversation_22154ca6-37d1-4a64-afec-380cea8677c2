// 自定义 tabBar
Component({
  data: {
    selected: 2, // 默认选中总览页面
    list: [
      {
        pagePath: "/pages/tasks/tasks",
        text: "任务",
        icon: "icon-task",
        selectedIcon: "icon-task-fill"
      },
      {
        pagePath: "/pages/workspace/workspace", 
        text: "工作台",
        icon: "icon-apps",
        selectedIcon: "icon-apps-fill"
      },
      {
        pagePath: "/pages/index/index",
        text: "总览", 
        icon: "icon-home",
        selectedIcon: "icon-home-fill"
      },
      {
        pagePath: "/pages/messages/messages",
        text: "消息",
        icon: "icon-chat",
        selectedIcon: "icon-chat-fill",
        badge: "" // 可以显示未读消息数
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        icon: "icon-user",
        selectedIcon: "icon-user-fill"
      }
    ]
  },

  methods: {
    switchTab(e) {
      const index = e.currentTarget.dataset.index
      const pagePath = this.data.list[index].pagePath
      
      wx.switchTab({
        url: pagePath,
        success: () => {
          this.setData({
            selected: index
          })
        }
      })
    },

    // 更新选中状态
    updateSelected(index) {
      this.setData({
        selected: index
      })
    },

    // 更新消息徽章
    updateBadge(index, badge) {
      const list = this.data.list
      list[index].badge = badge
      this.setData({
        list
      })
    }
  }
})
