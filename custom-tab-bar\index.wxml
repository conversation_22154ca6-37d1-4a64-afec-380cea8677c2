<!-- 自定义 tabBar -->
<view class="tab-bar">
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item {{selected === index ? 'selected' : ''}}" bindtap="switchTab" data-index="{{index}}">
    <!-- 图标 -->
    <view class="tab-bar-icon">
      <van-icon name="{{selected === index ? item.selectedIcon : item.icon}}" size="20px" color="{{selected === index ? '#4CAF50' : '#999999'}}" />
    </view>

    <!-- 文字 -->
    <view class="tab-bar-text">{{item.text}}</view>

    <!-- 红点提示 -->
    <view wx:if="{{item.badge}}" class="tab-bar-badge">{{item.badge}}</view>
  </view>
</view>
