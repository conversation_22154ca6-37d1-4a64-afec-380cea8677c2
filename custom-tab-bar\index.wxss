/* 自定义 tabBar 样式 */

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 98rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 8rpx 0;
  transition: all 0.2s ease;
}

.tab-bar-item.selected {
  color: #4CAF50;
}

.tab-bar-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
}

/* 移除了 iconfont 相关样式，现在使用 van-icon */

.tab-bar-text {
  font-size: 20rpx;
  color: #999999;
  transition: color 0.2s ease;
}

.tab-bar-item.selected .tab-bar-text {
  color: #4CAF50;
}

.tab-bar-badge {
  position: absolute;
  top: 4rpx;
  right: 20rpx;
  background-color: #ff4444;
  color: #ffffff;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  transform: scale(0.8);
}

/* 移除了 emoji 图标相关样式，现在使用 Vant Icon */
