# 性能优化指南

## 分包策略

### 主包 (Main Package)
- **大小限制**: 建议不超过 2MB
- **包含页面**:
  - `pages/index/index` - 首页总览
  - `pages/tasks/tasks` - 任务管理  
  - `pages/workspace/workspace` - 工作台

### 用户分包 (User Subpackage)
- **路径**: `subpages/user/`
- **大小限制**: 建议不超过 2MB
- **包含页面**:
  - `messages/messages` - 消息中心
  - `profile/profile` - 个人中心

### 业务分包 (Business Subpackage)  
- **路径**: `subpages/business/`
- **大小限制**: 建议不超过 2MB
- **包含页面**: 预留给未来业务功能扩展

## Vant UI 按需引入

### 组件使用原则
1. **按页面需求引入**: 每个页面只引入实际使用的组件
2. **避免全局引入**: 不在 app.json 中全局引入所有组件
3. **组件复用**: 优先使用高频组件，减少总体积

### 各页面组件配置

#### 任务页面
```json
{
  "van-search": "搜索框",
  "van-empty": "空状态",
  "van-loading": "加载状态", 
  "van-tag": "状态标签",
  "van-progress": "进度条",
  "van-button": "操作按钮"
}
```

#### 首页
```json
{
  "van-grid": "网格布局",
  "van-grid-item": "网格项",
  "van-card": "卡片",
  "van-tag": "标签",
  "van-progress": "进度条",
  "van-notice-bar": "通知栏"
}
```

#### 工作台
```json
{
  "van-grid": "功能网格",
  "van-cell": "列表项",
  "van-cell-group": "列表组",
  "van-icon": "图标",
  "van-info": "徽章"
}
```

## 预加载策略

### 智能预加载
- **首页**: 预加载用户分包（用户常访问消息和个人中心）
- **任务页**: 预加载业务分包（可能查看任务详情）
- **工作台**: WiFi环境下预加载用户分包

### 网络条件适配
- **WiFi**: 积极预加载
- **4G**: 适度预加载
- **3G及以下**: 按需加载

## 性能监控

### 关键指标
1. **启动时间**: 主包加载时间
2. **页面切换**: 分包加载时间
3. **内存使用**: 运行时内存占用
4. **网络请求**: API响应时间

### 监控工具
- 使用 `utils/performance.js` 进行性能监控
- 在页面中混入 `performanceMixin` 自动收集数据

## 优化建议

### 代码优化
1. **图片压缩**: 使用 WebP 格式，压缩图片大小
2. **代码分割**: 按功能模块拆分代码
3. **懒加载**: 非关键资源延迟加载
4. **缓存策略**: 合理使用本地缓存

### 资源优化
1. **字体图标**: 只包含使用的图标
2. **CSS优化**: 移除未使用的样式
3. **JavaScript**: 压缩和混淆代码
4. **依赖管理**: 定期清理无用依赖

## 构建命令

```bash
# 分析项目结构和大小
npm run analyze

# 构建生产版本
npm run build

# 性能优化
npm run optimize
```

## 最佳实践

### 开发阶段
1. 新增页面时考虑分包归属
2. 引入组件时评估必要性
3. 定期运行性能分析

### 发布前
1. 运行完整的性能分析
2. 检查包大小是否超限
3. 验证分包加载正常

### 监控指标
- 主包 < 2MB
- 分包 < 2MB  
- 总大小 < 20MB
- 启动时间 < 3s
- 页面切换 < 1s

## 故障排除

### 常见问题
1. **分包路径错误**: 检查 app.json 中的路径配置
2. **组件引入失败**: 确认组件路径和版本
3. **预加载失效**: 检查网络条件和预加载规则

### 调试工具
- 微信开发者工具性能面板
- 真机调试性能监控
- 自定义性能监控工具
