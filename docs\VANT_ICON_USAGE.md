# Vant Icon 使用指南

本项目已将所有 emoji 图标替换为 Vant Icon，提供更专业和一致的视觉体验。

## 基本用法

### 1. 引入组件

在页面的 `.json` 文件中引入 van-icon 组件：

```json
{
  "usingComponents": {
    "van-icon": "@vant/weapp/icon/index"
  }
}
```

### 2. 使用图标

在 `.wxml` 文件中使用：

```xml
<!-- 基本用法 -->
<van-icon name="chat-o" />

<!-- 设置图标大小和颜色 -->
<van-icon name="chat-o" size="20px" color="#1989fa" />

<!-- 自定义样式类 -->
<van-icon name="chat-o" custom-class="my-icon" />
```

## 项目中的图标替换对照表

### TabBar 图标
- 任务：`orders-o`
- 工作台：`apps-o`
- 总览：`wap-home-o`
- 消息：`chat-o`
- 我的：`user-o`

### 首页图标
- 位置：`location-o`
- 农场：`shop-o`
- 田块：`apps-o`
- 数据统计：`bar-chart-o`
- 任务管理：`orders-o`
- 设备监控：`setting-o`
- 作物管理：`flower-o`
- 数据报表：`bar-chart-o`

### 工作台图标
- 添加农机：`add-o`
- 作业查询：`search`
- 作业维护：`setting-o`
- 运输停车：`logistics`
- 生产计划：`orders-o`
- 人工巡田：`friends-o`
- 无人巡田：`send-gift-o`
- 天气预报：`fire-o`
- 市场行情：`bar-chart-o`
- 农技知识：`records`
- 计算工具：`balance-o`
- 地图导航：`location-o`
- 通讯录：`phone-o`

### 消息类型图标
- 警告：`warning-o` (橙色)
- 信息：`info-o` (蓝色)
- 成功：`success` (绿色)
- 错误：`close` (红色)

## 常用图标推荐

### 农业相关
- `flower-o` - 作物/植物
- `shop-o` - 农场/商店
- `logistics` - 运输/物流
- `fire-o` - 天气/温度

### 功能操作
- `add-o` - 添加
- `search` - 搜索
- `setting-o` - 设置
- `orders-o` - 订单/任务
- `location-o` - 位置
- `phone-o` - 电话

### 数据展示
- `bar-chart-o` - 图表
- `records` - 记录
- `balance-o` - 计算

### 状态提示
- `success` - 成功
- `warning-o` - 警告
- `info-o` - 信息
- `close` - 错误/关闭

## 图标属性

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| name | 图标名称 | string | - |
| dot | 是否显示图标右上角小红点 | boolean | false |
| info | 图标右上角徽标的内容 | string \| number | - |
| color | 图标颜色 | string | inherit |
| size | 图标大小，如 20px 2em，默认单位为px | string \| number | inherit |
| class-prefix | 类名前缀，用于使用自定义图标 | string | van-icon |
| custom-style | 自定义样式 | string | - |
| custom-class | 自定义样式类 | string | - |

## 更多图标

查看完整的图标列表，请访问：
https://vant-ui.github.io/vant-weapp/#/icon

## 注意事项

1. 图标名称必须是 Vant 支持的图标名称
2. 建议统一使用 `-o` 后缀的线性图标，保持视觉一致性
3. 颜色建议使用项目主题色：`#4CAF50`（绿色）、`#2196F3`（蓝色）、`#FF9800`（橙色）等
4. 大小建议使用：16px（小图标）、20px（中等图标）、24px（大图标）、32px（特大图标）
