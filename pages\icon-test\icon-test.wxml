<!-- 图标测试页面 -->
<view class="container">
  <view class="section">
    <view class="section-title">TabBar 图标测试</view>
    <view class="icon-grid">
      <view class="icon-item">
        <van-icon name="orders-o" size="40rpx" color="#4CAF50" />
        <text>任务</text>
      </view>
      <view class="icon-item">
        <van-icon name="apps-o" size="40rpx" color="#4CAF50" />
        <text>工作台</text>
      </view>
      <view class="icon-item">
        <van-icon name="wap-home-o" size="40rpx" color="#4CAF50" />
        <text>总览</text>
      </view>
      <view class="icon-item">
        <van-icon name="chat-o" size="40rpx" color="#4CAF50" />
        <text>消息</text>
      </view>
      <view class="icon-item">
        <van-icon name="user-o" size="40rpx" color="#4CAF50" />
        <text>我的</text>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">功能图标测试</view>
    <view class="icon-grid">
      <view class="icon-item">
        <van-icon name="location-o" size="48rpx" color="#666" />
        <text>位置</text>
      </view>
      <view class="icon-item">
        <van-icon name="shop-o" size="48rpx" color="#4CAF50" />
        <text>农场</text>
      </view>
      <view class="icon-item">
        <van-icon name="bar-chart-o" size="48rpx" color="#4CAF50" />
        <text>统计</text>
      </view>
      <view class="icon-item">
        <van-icon name="flower-o" size="48rpx" color="#4CAF50" />
        <text>作物</text>
      </view>
      <view class="icon-item">
        <van-icon name="logistics" size="48rpx" color="#4CAF50" />
        <text>物流</text>
      </view>
      <view class="icon-item">
        <van-icon name="search" size="48rpx" color="#4CAF50" />
        <text>搜索</text>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">状态图标测试</view>
    <view class="icon-grid">
      <view class="icon-item">
        <van-icon name="warning-o" size="48rpx" color="#FF9800" />
        <text>警告</text>
      </view>
      <view class="icon-item">
        <van-icon name="info-o" size="48rpx" color="#2196F3" />
        <text>信息</text>
      </view>
      <view class="icon-item">
        <van-icon name="success" size="48rpx" color="#4CAF50" />
        <text>成功</text>
      </view>
      <view class="icon-item">
        <van-icon name="close" size="48rpx" color="#F44336" />
        <text>错误</text>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">新增图标测试</view>
    <view class="icon-grid">
      <view class="icon-item">
        <van-icon name="calendar-o" size="48rpx" color="#666" />
        <text>日期</text>
      </view>
      <view class="icon-item">
        <van-icon name="plus" size="48rpx" color="#4CAF50" />
        <text>添加</text>
      </view>
      <view class="icon-item">
        <van-icon name="scan" size="48rpx" color="#4CAF50" />
        <text>扫码</text>
      </view>
      <view class="icon-item">
        <van-icon name="exchange" size="48rpx" color="#4CAF50" />
        <text>切换</text>
      </view>
      <view class="icon-item">
        <van-icon name="arrow-left" size="48rpx" color="#666" />
        <text>返回</text>
      </view>
      <view class="icon-item">
        <van-icon name="question-o" size="48rpx" color="#666" />
        <text>帮助</text>
      </view>
    </view>
  </view>
</view>
