<!-- 首页 - 总览 -->
<nav-bar title="智慧农场" show-back="{{false}}" bind:navbarheight="onNavBarHeight"></nav-bar>

<view class="container" style="padding-top: {{navBarHeight}}px;">
  <!-- 天气卡片 -->
  <view class="weather-card">
    <view class="weather-location">
      <van-icon name="location-o" size="32rpx" color="#666" custom-class="weather-location-icon" />
      <text class="weather-location-text">{{location}}</text>
    </view>
    
    <view class="weather-main">
      <view class="weather-left">
        <view class="weather-temp">{{weather.temperature}}</view>
        <view class="weather-condition">{{weather.condition}} | {{weather.humidity}}</view>
        <view class="weather-date">{{weather.date}}</view>
      </view>
      
      <view class="weather-right">
        <view class="weather-farm">
          <van-icon name="shop-o" size="32rpx" color="#4CAF50" custom-class="weather-farm-icon" />
          <text>{{farmName}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="stats-grid">
    <view class="stats-card">
      <view class="stats-label">农机辆</view>
      <view class="stats-value">
        {{farmStats.machinery.count}}
        <text class="stats-unit">在线 {{farmStats.machinery.inUse}}</text>
      </view>
    </view>
    
    <view class="stats-card">
      <view class="stats-label">投入品/种</view>
      <view class="stats-value">{{farmStats.inputs.count}}</view>
    </view>
    
    <view class="stats-card">
      <view class="stats-label">作物种</view>
      <view class="stats-value">{{farmStats.crops.count}}</view>
    </view>
  </view>

  <!-- 田块数据 -->
  <view class="data-card">
    <view class="data-row">
      <view class="data-label">
        <van-icon name="apps-o" size="40rpx" color="#4CAF50" custom-class="data-icon" />
        <text>田块数/块</text>
      </view>
      <view class="data-value">{{farmStats.fieldArea}}</view>
    </view>

    <view class="data-row">
      <view class="data-label">
        <van-icon name="bar-chart-o" size="40rpx" color="#4CAF50" custom-class="data-icon" />
        <text>田块面积/亩</text>
      </view>
      <view class="data-value">{{farmStats.totalRevenue}}</view>
    </view>
  </view>

  <!-- 种植规模 -->
  <view class="card">
    <view class="card-header">
      <view class="card-title">种植规模</view>
    </view>
    
    <view class="card-body">
      <view wx:for="{{crops}}" wx:key="name" class="crop-item">
        <view class="crop-name">{{item.name}}</view>
        <view class="crop-progress">
          <view class="progress">
            <view class="progress-bar" style="width: {{item.progress * 100}}%; background-color: {{item.color || '#4CAF50'}};"></view>
          </view>
        </view>
        <view class="crop-area">{{item.area}}亩</view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="card">
    <view class="card-header">
      <view class="card-title">快捷操作</view>
    </view>
    
    <view class="card-body">
      <view class="workspace-grid">
        <view class="workspace-item" bindtap="navigateToTasks">
          <van-icon name="orders-o" size="56rpx" color="#4CAF50" custom-class="workspace-icon" />
          <view class="workspace-title">任务管理</view>
        </view>

        <view class="workspace-item" bindtap="navigateToDevices">
          <van-icon name="setting-o" size="56rpx" color="#4CAF50" custom-class="workspace-icon" />
          <view class="workspace-title">设备监控</view>
        </view>

        <view class="workspace-item" bindtap="navigateToCrops">
          <van-icon name="flower-o" size="56rpx" color="#4CAF50" custom-class="workspace-icon" />
          <view class="workspace-title">作物管理</view>
        </view>

        <view class="workspace-item" bindtap="navigateToReports">
          <van-icon name="bar-chart-o" size="56rpx" color="#4CAF50" custom-class="workspace-icon" />
          <view class="workspace-title">数据报表</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 最新动态 -->
  <view class="card" wx:if="{{recentActivities.length > 0}}">
    <view class="card-header">
      <view class="card-title">最新动态</view>
    </view>
    
    <view class="card-body">
      <view wx:for="{{recentActivities}}" wx:key="id" class="list-item">
        <view class="list-item-content">
          <view class="list-item-title">{{item.title}}</view>
          <view class="list-item-desc">{{item.description}}</view>
        </view>
        <view class="list-item-extra">{{item.time}}</view>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading">
  <text class="loading-text">加载中...</text>
</view>

<!-- 空状态 -->
<view wx:if="{{!loading && isEmpty}}" class="empty">
  <van-icon name="bar-chart-o" size="96rpx" color="#ccc" custom-class="empty-icon" />
  <view class="empty-text">暂无数据</view>
</view>
