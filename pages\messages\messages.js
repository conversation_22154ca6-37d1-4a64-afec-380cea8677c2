// 消息页面
import api from '../../api/index.js'
import store from '../../utils/store.js'
import { showToast, showConfirm, debounce } from '../../utils/common.js'

Page({
  data: {
    // 导航栏高度
    navBarHeight: 0,

    // 当前标签
    currentTab: 'all',
    
    // 搜索关键词
    searchKeyword: '',
    
    // 消息列表
    messageList: [],
    
    // 页面状态
    loading: true,
    
    // 分页参数
    page: 1,
    pageSize: 20,
    hasMore: true
  },

  // 页面加载
  onLoad: function (options) {
    console.log('Messages page loaded')
    this.initPage()
  },

  // 页面显示
  onShow: function () {
    console.log('Messages page shown')
    this.refreshData()

    // 更新 tabBar 选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().updateSelected(3) // 消息页面索引为3
    }
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData()
    }
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '农场消息中心',
      path: '/pages/messages/messages'
    }
  },

  // 初始化页面
  initPage: function () {
    // 订阅store变化
    this.subscribeStore()

    // 加载数据
    this.loadData()
  },

  // 导航栏高度回调
  onNavBarHeight: function(e) {
    this.setData({
      navBarHeight: e.detail.navBarHeight
    })
  },

  // 订阅store变化
  subscribeStore: function () {
    store.subscribe('messages', (messagesData) => {
      if (messagesData.list) {
        this.setData({
          messageList: messagesData.list
        })
      }
    })
  },

  // 加载数据
  loadData: function () {
    this.setData({ 
      loading: true,
      page: 1,
      hasMore: true
    })
    
    const params = {
      page: 1,
      pageSize: this.data.pageSize,
      type: this.data.currentTab === 'all' ? '' : this.data.currentTab,
      keyword: this.data.searchKeyword
    }
    
    return api.message.getMessageList(params).then((response) => {
      const messages = response.data || []
      this.setData({
        messageList: messages,
        loading: false,
        hasMore: messages.length >= this.data.pageSize
      })
      
      // 更新store
      store.setState({
        messages: {
          list: messages,
          unreadCount: messages.filter(msg => !msg.read).length
        }
      })
    }).catch((error) => {
      console.error('Load messages failed:', error)
      this.setData({ loading: false })
      showToast('加载失败')
    })
  },

  // 刷新数据
  refreshData: function () {
    return this.loadData()
  },

  // 加载更多数据
  loadMoreData: function () {
    const nextPage = this.data.page + 1
    const params = {
      page: nextPage,
      pageSize: this.data.pageSize,
      type: this.data.currentTab === 'all' ? '' : this.data.currentTab,
      keyword: this.data.searchKeyword
    }
    
    return api.message.getMessageList(params).then((response) => {
      const newMessages = response.data || []
      const allMessages = [...this.data.messageList, ...newMessages]
      
      this.setData({
        messageList: allMessages,
        page: nextPage,
        hasMore: newMessages.length >= this.data.pageSize
      })
      
      // 更新store
      store.setState({
        messages: {
          list: allMessages,
          unreadCount: allMessages.filter(msg => !msg.read).length
        }
      })
    }).catch((error) => {
      console.error('Load more messages failed:', error)
      showToast('加载更多失败')
    })
  },

  // 切换标签
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab
    if (tab !== this.data.currentTab) {
      this.setData({ currentTab: tab })
      this.loadData()
    }
  },

  // 搜索输入
  onSearchInput: debounce(function (e) {
    const keyword = e.detail.value.trim()
    this.setData({ searchKeyword: keyword })
    this.loadData()
  }, 500),

  // 清除搜索
  onSearchClear: function () {
    this.setData({ searchKeyword: '' })
    this.loadData()
  },

  // 点击消息
  onMessageTap: function (e) {
    const message = e.currentTarget.dataset.message
    
    // 标记为已读
    if (!message.read) {
      this.markMessageRead(message.id)
    }
    
    // 跳转到消息详情或相关页面
    this.handleMessageAction(message)
  },

  // 处理消息操作
  handleMessageAction: function (message) {
    switch (message.type) {
      case 'task':
        // 跳转到任务详情
        wx.navigateTo({
          url: `/pages/task-detail/task-detail?id=${message.relatedId}`
        })
        break
      case 'weather':
        // 跳转到天气页面
        wx.navigateTo({
          url: '/pages/weather/weather'
        })
        break
      case 'system':
        // 显示系统消息详情
        wx.showModal({
          title: message.title,
          content: message.content,
          showCancel: false
        })
        break
      default:
        // 显示消息详情
        wx.navigateTo({
          url: `/pages/message-detail/message-detail?id=${message.id}`
        })
        break
    }
  },

  // 标记消息已读
  markMessageRead: function (messageId) {
    return api.message.markMessageRead(messageId).then(() => {
      // 更新本地数据
      const messageList = this.data.messageList.map(msg => {
        if (msg.id === messageId) {
          return { ...msg, read: true }
        }
        return msg
      })
      
      this.setData({ messageList })
      
      // 更新store
      store.setState({
        messages: {
          list: messageList,
          unreadCount: messageList.filter(msg => !msg.read).length
        }
      })
    }).catch((error) => {
      console.error('Mark message read failed:', error)
    })
  },

  // 全部已读
  markAllRead: function () {
    const unreadMessages = this.data.messageList.filter(msg => !msg.read)
    if (unreadMessages.length === 0) {
      showToast('没有未读消息')
      return
    }
    
    showConfirm('确定要标记所有消息为已读吗？').then((confirmed) => {
      if (confirmed) {
        api.message.markAllRead().then(() => {
          // 更新本地数据
          const messageList = this.data.messageList.map(msg => ({
            ...msg,
            read: true
          }))
          
          this.setData({ messageList })
          
          // 更新store
          store.setState({
            messages: {
              list: messageList,
              unreadCount: 0
            }
          })
          
          showToast('已全部标记为已读')
        }).catch((error) => {
          console.error('Mark all read failed:', error)
          showToast('操作失败')
        })
      }
    })
  },

  // 清空消息
  deleteAll: function () {
    if (this.data.messageList.length === 0) {
      showToast('没有消息可清空')
      return
    }
    
    showConfirm('确定要清空所有消息吗？此操作不可恢复。').then((confirmed) => {
      if (confirmed) {
        // 这里应该调用API删除所有消息
        // 目前只是清空本地数据
        this.setData({ messageList: [] })
        
        // 更新store
        store.setState({
          messages: {
            list: [],
            unreadCount: 0
          }
        })
        
        showToast('消息已清空')
      }
    })
  }
})
