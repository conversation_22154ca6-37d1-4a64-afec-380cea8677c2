<!-- 消息页面 -->
<nav-bar title="消息" bind:navbarheight="onNavBarHeight"></nav-bar>

<view class="container" style="padding-top: {{navBarHeight}}px;">
  <!-- 消息分类标签 -->
  <view class="message-tabs">
    <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="all">
      <text>进度异常</text>
    </view>
    <view class="tab-item {{currentTab === 'system' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="system">
      <text>巡田异常</text>
    </view>
    <view class="tab-item {{currentTab === 'weather' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="weather">
      <text>气象灾害</text>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-input-wrapper">
      <van-icon name="search" size="16px" color="#999" custom-class="search-icon" />
      <input class="search-input"
             placeholder="搜索消息内容"
             value="{{searchKeyword}}"
             bindinput="onSearchInput" />
      <view wx:if="{{searchKeyword}}" class="search-clear" bindtap="onSearchClear">
        <van-icon name="clear" size="16px" color="#999" />
      </view>
    </view>
  </view>

  <!-- 消息列表 -->
  <view class="message-list">
    <view wx:for="{{messageList}}" wx:key="id" 
          class="message-item {{!item.read ? 'unread' : ''}}"
          bindtap="onMessageTap" 
          data-message="{{item}}">
      
      <!-- 消息类型图标 -->
      <view class="message-icon">
        <van-icon wx:if="{{item.type === 'warning'}}" name="warning-o" size="20px" color="#FF9800" />
        <van-icon wx:if="{{item.type === 'info'}}" name="info-o" size="20px" color="#2196F3" />
        <van-icon wx:if="{{item.type === 'success'}}" name="success" size="20px" color="#4CAF50" />
        <van-icon wx:if="{{item.type === 'error'}}" name="close" size="20px" color="#F44336" />
      </view>

      <!-- 消息内容 -->
      <view class="message-content">
        <view class="message-title">{{item.title}}</view>
        <view class="message-text">{{item.content}}</view>
        <view class="message-time">{{item.time}}</view>
      </view>

      <!-- 未读标识 -->
      <view wx:if="{{!item.read}}" class="unread-dot"></view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && messageList.length === 0}}" class="empty">
    <view class="empty-icon">📬</view>
    <view class="empty-text">{{searchKeyword ? '未找到相关消息' : '暂无消息'}}</view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions" wx:if="{{messageList.length > 0}}">
  <view class="action-item" bindtap="markAllRead">
    <text class="action-icon">✅</text>
    <text>全部已读</text>
  </view>

  <view class="action-item" bindtap="deleteAll">
    <text class="action-icon">🗑️</text>
    <text>清空消息</text>
  </view>
</view>
