/* 消息页面样式 */

.container {
  background-color: var(--bg-color);
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 消息分类标签 */
.message-tabs {
  display: flex;
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-light);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-base);
  font-size: var(--font-size-base);
  color: var(--text-muted);
  position: relative;
  transition: all var(--transition-fast);
}

.tab-item.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: 2rpx;
}

/* 搜索区域 */
.search-section {
  padding: var(--spacing-base);
  background-color: var(--bg-white);
  border-bottom: 1rpx solid var(--border-light);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-sm) var(--spacing-base);
}

.search-icon {
  font-size: 28rpx;
  margin-right: var(--spacing-sm);
  opacity: 0.5;
}

.search-input {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--text-color);
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--spacing-sm);
}

.search-clear text {
  font-size: 20rpx;
  opacity: 0.5;
}

/* 消息列表 */
.message-list {
  padding: var(--spacing-base);
}

.message-item {
  display: flex;
  align-items: flex-start;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-base);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  position: relative;
}

.message-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

.message-item.unread {
  border-left: 4rpx solid var(--primary-color);
  background-color: #F8F9FF;
}

/* 消息图标 */
.message-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-base);
  border-radius: 50%;
  background-color: var(--bg-gray);
}

.message-icon text {
  font-size: 32rpx;
}

/* 消息内容 */
.message-content {
  flex: 1;
  min-width: 0; /* 防止文本溢出 */
}

.message-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
  line-height: 1.4;
}

.message-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* 未读标识 */
.unread-dot {
  position: absolute;
  top: var(--spacing-base);
  right: var(--spacing-base);
  width: 16rpx;
  height: 16rpx;
  background-color: var(--danger-color);
  border-radius: 50%;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-white);
  border-top: 1rpx solid var(--border-light);
  display: flex;
  padding: var(--spacing-base);
  z-index: 100;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  transition: all var(--transition-fast);
}

.action-item:active {
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-base);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing-xs);
  opacity: 0.7;
  display: block;
  text-align: center;
}

.action-item text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 空状态 */
.empty {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-base);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: var(--spacing-base);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-base);
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-text {
  margin-left: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .message-icon {
    width: 56rpx;
    height: 56rpx;
    margin-right: var(--spacing-sm);
  }
  
  .message-icon image {
    width: 28rpx;
    height: 28rpx;
  }
  
  .message-title {
    font-size: var(--font-size-sm);
  }
  
  .message-text {
    font-size: var(--font-size-xs);
  }
  
  .action-item image {
    width: 40rpx;
    height: 40rpx;
  }
  
  .action-item text {
    font-size: var(--font-size-xs);
  }
}
