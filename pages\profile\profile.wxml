<!-- 我的页面 -->
<nav-bar title="我的" show-back="{{false}}" bind:navbarheight="onNavBarHeight"></nav-bar>

<view class="container" style="padding-top: {{navBarHeight}}px;">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="user-avatar" 
             src="{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}" 
             mode="aspectFill"
             bindtap="onAvatarTap"></image>
      
      <view class="user-details">
        <view class="user-name">{{userInfo.nickName || '王培乾'}}</view>
        <view class="user-id">账号：{{userInfo.account || '***78**56**'}}</view>
        <view class="user-wechat">微信appid：{{userInfo.wechatId || '***10b9b8***'}}</view>
      </view>
      
      <view class="copy-buttons">
        <view class="copy-btn" bindtap="copyAccount">复制</view>
        <view class="copy-btn" bindtap="copyWechatId">复制</view>
      </view>
    </view>
  </view>

  <!-- 切换农场 -->
  <view class="farm-switch">
    <view class="farm-switch-item" bindtap="switchFarm">
      <van-icon name="exchange" size="16px" color="#4CAF50" custom-class="farm-switch-icon" />
      <text class="farm-switch-text">切换农场</text>
      <van-icon name="arrow" size="12px" color="#ccc" custom-class="farm-switch-arrow" />
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="navigateTo" data-url="/pages/settings/settings">
      <view class="menu-item-left">
        <van-icon name="setting-o" size="18px" color="#666" custom-class="menu-icon" />
        <text class="menu-text">设置</text>
      </view>
      <van-icon name="arrow" size="12px" color="#ccc" custom-class="menu-arrow" />
    </view>

    <view class="menu-item" bindtap="navigateTo" data-url="/pages/help/help">
      <view class="menu-item-left">
        <van-icon name="question-o" size="18px" color="#666" custom-class="menu-icon" />
        <text class="menu-text">帮助与反馈</text>
      </view>
      <van-icon name="arrow" size="12px" color="#ccc" custom-class="menu-arrow" />
    </view>

    <view class="menu-item" bindtap="navigateTo" data-url="/pages/about/about">
      <view class="menu-item-left">
        <van-icon name="info-o" size="18px" color="#666" custom-class="menu-icon" />
        <text class="menu-text">关于我们</text>
      </view>
      <van-icon name="arrow" size="12px" color="#ccc" custom-class="menu-arrow" />
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="section-title">我的数据</view>
    
    <view class="stats-grid">
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-tasks/my-tasks">
        <view class="stats-value">{{userStats.tasks}}</view>
        <view class="stats-label">我的任务</view>
      </view>
      
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-reports/my-reports">
        <view class="stats-value">{{userStats.reports}}</view>
        <view class="stats-label">我的报告</view>
      </view>
      
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-records/my-records">
        <view class="stats-value">{{userStats.records}}</view>
        <view class="stats-label">操作记录</view>
      </view>
      
      <view class="stats-item" bindtap="navigateTo" data-url="/pages/my-achievements/my-achievements">
        <view class="stats-value">{{userStats.achievements}}</view>
        <view class="stats-label">我的成就</view>
      </view>
    </view>
  </view>

  <!-- 快捷工具 -->
  <view class="tools-section">
    <view class="section-title">快捷工具</view>
    
    <view class="tools-grid">
      <view class="tool-item" bindtap="scanCode">
        <van-icon name="scan" size="24px" color="#4CAF50" custom-class="tool-icon" />
        <text class="tool-text">扫一扫</text>
      </view>

      <view class="tool-item" bindtap="shareApp">
        <van-icon name="share-o" size="24px" color="#4CAF50" custom-class="tool-icon" />
        <text class="tool-text">分享应用</text>
      </view>

      <view class="tool-item" bindtap="contactService">
        <van-icon name="phone-o" size="24px" color="#4CAF50" custom-class="tool-icon" />
        <text class="tool-text">联系客服</text>
      </view>

      <view class="tool-item" bindtap="feedback">
        <van-icon name="chat-o" size="24px" color="#4CAF50" custom-class="tool-icon" />
        <text class="tool-text">意见反馈</text>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">版本 {{version}}</text>
  </view>
</view>
