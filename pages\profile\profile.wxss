/* 我的页面样式 */

.container {
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--text-white);
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: var(--spacing-base);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.user-id,
.user-wechat {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.user-wechat:last-child {
  margin-bottom: 0;
}

.copy-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.copy-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-xs);
  text-align: center;
  min-width: 80rpx;
  transition: all var(--transition-fast);
}

.copy-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 切换农场 */
.farm-switch {
  background-color: var(--bg-white);
  margin: var(--spacing-base);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.farm-switch-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-base);
  transition: background-color var(--transition-fast);
}

.farm-switch-item:active {
  background-color: var(--bg-gray);
}

.farm-switch-icon {
  font-size: 32rpx;
  margin-right: var(--spacing-base);
  opacity: 0.7;
}

.farm-switch-text {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--text-color);
}

.farm-switch-arrow {
  font-size: 20rpx;
  opacity: 0.5;
}

/* 功能菜单 */
.menu-section {
  background-color: var(--bg-white);
  margin: var(--spacing-base);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-base);
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color var(--transition-fast);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: var(--bg-gray);
}

.menu-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  font-size: 32rpx;
  margin-right: var(--spacing-base);
  opacity: 0.7;
}

.menu-text {
  font-size: var(--font-size-base);
  color: var(--text-color);
}

.menu-arrow {
  font-size: 20rpx;
  opacity: 0.5;
}

/* 数据统计 */
.stats-section {
  margin: var(--spacing-base);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  margin-bottom: var(--spacing-base);
  padding: 0 var(--spacing-sm);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-base);
}

.stats-item {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all var(--transition-fast);
}

.stats-item:active {
  transform: scale(0.98);
  background-color: var(--bg-gray);
}

.stats-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* 快捷工具 */
.tools-section {
  margin: var(--spacing-base);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-base);
}

.tool-item {
  background-color: var(--bg-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  text-align: center;
  transition: all var(--transition-fast);
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tool-item:active {
  transform: scale(0.95);
  background-color: var(--bg-gray);
}

.tool-icon {
  font-size: 48rpx;
  margin-bottom: var(--spacing-xs);
  opacity: 0.7;
  display: block;
  text-align: center;
}

.tool-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: var(--spacing-lg);
}

.version-text {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .user-avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .user-name {
    font-size: var(--font-size-base);
  }
  
  .user-id,
  .user-wechat {
    font-size: var(--font-size-xs);
  }
  
  .copy-btn {
    font-size: 20rpx;
    padding: 4rpx var(--spacing-xs);
    min-width: 60rpx;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  
  .tools-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
  }
  
  .tool-item {
    padding: var(--spacing-sm);
    min-height: 100rpx;
  }
  
  .tool-icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  .tool-text {
    font-size: var(--font-size-xs);
  }
}
