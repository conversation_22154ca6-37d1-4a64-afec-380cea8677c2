<!-- 工作台页面 -->
<nav-bar title="工作台" bind:navbarheight="onNavBarHeight"></nav-bar>

<view class="container" style="padding-top: {{navBarHeight}}px;">
  <!-- 农机管理 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">农机管理</view>
    </view>
    
    <view class="workspace-grid">
      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/machinery/machinery">
        <van-icon name="add-o" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">添加农机</view>
      </view>

      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/machinery-query/machinery-query">
        <van-icon name="search" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">作业查询</view>
      </view>

      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/machinery-maintenance/machinery-maintenance">
        <van-icon name="setting-o" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">作业维护</view>
      </view>

      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/transport/transport">
        <van-icon name="logistics" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">运输停车</view>
      </view>
    </view>
  </view>

  <!-- 生产管理 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">生产管理</view>
    </view>
    
    <view class="workspace-grid">
      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/production-plan/production-plan">
        <van-icon name="orders-o" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">生产计划</view>
      </view>
    </view>
  </view>

  <!-- 田间管理 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">田间管理</view>
    </view>
    
    <view class="workspace-grid">
      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/field-patrol/field-patrol">
        <van-icon name="friends-o" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">人工巡田</view>
      </view>

      <view class="workspace-item" bindtap="navigateTo" data-url="/pages/drone-patrol/drone-patrol">
        <van-icon name="send-gift-o" size="24px" color="#4CAF50" custom-class="workspace-icon" />
        <view class="workspace-title">无人巡田</view>
      </view>
    </view>
  </view>

  <!-- 快捷功能 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">快捷功能</view>
    </view>
    
    <view class="quick-actions">
      <view class="quick-action-item" bindtap="navigateTo" data-url="/pages/weather/weather">
        <van-icon name="fire-o" size="20px" color="#FF9800" custom-class="quick-action-icon" />
        <text class="quick-action-text">天气预报</text>
      </view>

      <view class="quick-action-item" bindtap="navigateTo" data-url="/pages/market/market">
        <van-icon name="bar-chart-o" size="20px" color="#FF9800" custom-class="quick-action-icon" />
        <text class="quick-action-text">市场行情</text>
      </view>

      <view class="quick-action-item" bindtap="navigateTo" data-url="/pages/knowledge/knowledge">
        <van-icon name="records" size="20px" color="#FF9800" custom-class="quick-action-icon" />
        <text class="quick-action-text">农技知识</text>
      </view>

      <view class="quick-action-item" bindtap="navigateTo" data-url="/pages/calculator/calculator">
        <van-icon name="balance-o" size="20px" color="#FF9800" custom-class="quick-action-icon" />
        <text class="quick-action-text">计算工具</text>
      </view>

      <view class="quick-action-item" bindtap="navigateTo" data-url="/pages/map/map">
        <van-icon name="location-o" size="20px" color="#FF9800" custom-class="quick-action-icon" />
        <text class="quick-action-text">地图导航</text>
      </view>

      <view class="quick-action-item" bindtap="navigateTo" data-url="/pages/contacts/contacts">
        <van-icon name="phone-o" size="20px" color="#FF9800" custom-class="quick-action-icon" />
        <text class="quick-action-text">通讯录</text>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="section">
    <view class="section-header">
      <view class="section-title">数据统计</view>
    </view>
    
    <view class="stats-cards">
      <view class="stats-card" bindtap="navigateTo" data-url="/pages/stats/machinery">
        <van-icon name="logistics" size="32px" color="#4CAF50" custom-class="stats-icon" />
        <view class="stats-content">
          <view class="stats-value">{{stats.machinery.total}}</view>
          <view class="stats-label">农机总数</view>
        </view>
        <view class="stats-trend stats-trend-up">
          <text>+{{stats.machinery.increase}}</text>
        </view>
      </view>

      <view class="stats-card" bindtap="navigateTo" data-url="/pages/stats/tasks">
        <van-icon name="orders-o" size="32px" color="#2196F3" custom-class="stats-icon" />
        <view class="stats-content">
          <view class="stats-value">{{stats.tasks.completed}}</view>
          <view class="stats-label">已完成任务</view>
        </view>
        <view class="stats-trend stats-trend-up">
          <text>+{{stats.tasks.increase}}</text>
        </view>
      </view>

      <view class="stats-card" bindtap="navigateTo" data-url="/pages/stats/area">
        <van-icon name="flower-o" size="32px" color="#FF9800" custom-class="stats-icon" />
        <view class="stats-content">
          <view class="stats-value">{{stats.area.total}}</view>
          <view class="stats-label">作业面积(亩)</view>
        </view>
        <view class="stats-trend stats-trend-up">
          <text>+{{stats.area.increase}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
