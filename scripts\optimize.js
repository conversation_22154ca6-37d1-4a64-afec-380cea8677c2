#!/usr/bin/env node

// 小程序构建优化脚本
// 用于分析和优化项目体积、性能

const fs = require('fs')
const path = require('path')

class WxOptimizer {
  constructor() {
    this.projectRoot = process.cwd()
    this.stats = {
      totalSize: 0,
      mainPackageSize: 0,
      subPackageSize: {},
      componentUsage: {},
      unusedFiles: []
    }
  }

  // 分析项目结构
  analyzeProject() {
    console.log('🔍 开始分析项目结构...')

    this.analyzeMainPackage()
    this.analyzeComponents()
    this.generateReport()
  }

  // 分析主包
  analyzeMainPackage() {
    const mainPages = ['pages/index', 'pages/tasks', 'pages/workspace']
    let mainSize = 0
    
    mainPages.forEach(pagePath => {
      const fullPath = path.join(this.projectRoot, pagePath)
      if (fs.existsSync(fullPath)) {
        mainSize += this.getDirectorySize(fullPath)
      }
    })
    
    // 添加公共资源大小
    const commonPaths = ['app.js', 'app.wxss', 'app.json', 'components', 'utils', 'api', 'config', 'styles']
    commonPaths.forEach(commonPath => {
      const fullPath = path.join(this.projectRoot, commonPath)
      if (fs.existsSync(fullPath)) {
        mainSize += this.getDirectorySize(fullPath)
      }
    })
    
    this.stats.mainPackageSize = mainSize
    console.log(`📦 主包大小: ${this.formatSize(mainSize)}`)
  }



  // 分析组件使用情况
  analyzeComponents() {
    console.log('🧩 分析组件使用情况...')
    
    const componentUsage = {}
    const pageFiles = this.getAllPageFiles()
    
    pageFiles.forEach(pageFile => {
      const configPath = pageFile.replace(/\.(js|wxml|wxss)$/, '.json')
      if (fs.existsSync(configPath)) {
        try {
          const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
          if (config.usingComponents) {
            Object.keys(config.usingComponents).forEach(component => {
              componentUsage[component] = (componentUsage[component] || 0) + 1
            })
          }
        } catch (e) {
          console.warn(`⚠️  无法解析配置文件: ${configPath}`)
        }
      }
    })
    
    this.stats.componentUsage = componentUsage
  }

  // 获取目录大小
  getDirectorySize(dirPath) {
    let totalSize = 0
    
    if (fs.statSync(dirPath).isFile()) {
      return fs.statSync(dirPath).size
    }
    
    const files = fs.readdirSync(dirPath)
    files.forEach(file => {
      const filePath = path.join(dirPath, file)
      const stats = fs.statSync(filePath)
      
      if (stats.isDirectory()) {
        totalSize += this.getDirectorySize(filePath)
      } else {
        totalSize += stats.size
      }
    })
    
    return totalSize
  }

  // 获取所有页面文件
  getAllPageFiles() {
    const pageFiles = []

    // 主包页面
    const mainPages = ['pages/index', 'pages/tasks', 'pages/workspace', 'pages/messages', 'pages/profile']
    mainPages.forEach(pagePath => {
      const fullPath = path.join(this.projectRoot, pagePath)
      if (fs.existsSync(fullPath)) {
        const files = fs.readdirSync(fullPath)
        files.forEach(file => {
          pageFiles.push(path.join(fullPath, file))
        })
      }
    })

    return pageFiles
  }

  // 遍历目录
  walkDirectory(dirPath, callback) {
    const files = fs.readdirSync(dirPath)
    files.forEach(file => {
      const filePath = path.join(dirPath, file)
      const stats = fs.statSync(filePath)
      
      if (stats.isDirectory()) {
        this.walkDirectory(filePath, callback)
      } else {
        callback(filePath)
      }
    })
  }

  // 格式化文件大小
  formatSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 生成优化报告
  generateReport() {
    console.log('\n📊 项目优化报告')
    console.log('=' * 50)
    
    // 包大小统计
    console.log('\n📦 包大小统计:')
    console.log(`主包: ${this.formatSize(this.stats.mainPackageSize)}`)
    console.log(`总大小: ${this.formatSize(this.stats.mainPackageSize)}`)
    
    // 组件使用统计
    console.log('\n🧩 组件使用统计:')
    const sortedComponents = Object.entries(this.stats.componentUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
    
    sortedComponents.forEach(([component, count]) => {
      console.log(`${component}: ${count}次`)
    })
    
    // 优化建议
    console.log('\n💡 优化建议:')
    
    if (this.stats.mainPackageSize > 2 * 1024 * 1024) {
      console.log('⚠️  主包大小超过2MB，建议优化资源')
    }
    
    const unusedComponents = Object.entries(this.stats.componentUsage)
      .filter(([, count]) => count === 1)
      .map(([component]) => component)
    
    if (unusedComponents.length > 0) {
      console.log(`💡 以下组件使用频率较低，可考虑合并: ${unusedComponents.slice(0, 5).join(', ')}`)
    }
    
    console.log('\n✅ 分析完成!')
  }

  // 清理未使用的文件
  cleanUnusedFiles() {
    console.log('🧹 清理未使用的文件...')
    // 这里可以添加清理逻辑
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const optimizer = new WxOptimizer()
  optimizer.analyzeProject()
}

module.exports = WxOptimizer
